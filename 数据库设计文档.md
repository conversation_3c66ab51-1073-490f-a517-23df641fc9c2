# RuoYi-AI 系统数据库设计文档

## 数据库概述

RuoYi-AI 教学系统使用 MySQL 8.0 作为主数据库，采用 InnoDB 存储引擎，字符集为 utf8mb4。数据库名称为 `course_ai`。

## 数据库表结构

### A7 课题相关表

#### 1. a7_paper (试卷信息表)
存储试卷的基本信息，包括试卷名称、难度、类型等。

```sql
CREATE TABLE `a7_paper` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` varchar(255) DEFAULT NULL COMMENT '创建部门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `paper_name` varchar(128) DEFAULT NULL COMMENT '试卷名称',
  `create_role` varchar(32) DEFAULT NULL COMMENT '创建角色(teacher/student)',
  `create_name` varchar(32) DEFAULT NULL COMMENT '创建人姓名',
  `paper_difficulty` varchar(32) DEFAULT NULL COMMENT '试卷难度(基础/中等/挑战)',
  `paper_type` varchar(32) DEFAULT NULL COMMENT '试卷类型(全部题型/编程题/选择题)',
  `paper_subject` varchar(128) DEFAULT NULL COMMENT '科目',
  `topic_number` int DEFAULT NULL COMMENT '题目数量',
  `answer_time` int DEFAULT NULL COMMENT '答题时间(分钟)',
  `collection` bit(1) DEFAULT b'0' COMMENT '是否收藏',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='试卷信息表';
```

**字段说明**:
- `paper_difficulty`: 试卷难度，枚举值：基础、中等、挑战
- `paper_type`: 试卷类型，枚举值：全部题型、编程题、选择题
- `create_role`: 创建者角色，用于权限控制
- `answer_time`: 答题时间限制，单位为分钟

#### 2. a7_topic (题目信息表)
存储试卷中的题目信息，支持选择题和编程题。

```sql
CREATE TABLE `a7_topic` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` varchar(255) DEFAULT NULL COMMENT '创建部门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `paper_id` bigint DEFAULT NULL COMMENT '关联试卷ID',
  `topic_name` varchar(200) DEFAULT NULL COMMENT '题目名称',
  `topic_difficulty` varchar(32) DEFAULT NULL COMMENT '题目难度(基础/中等/挑战)',
  `topic_type` varchar(32) DEFAULT NULL COMMENT '题目类型(选择题/编程题)',
  `topic_content` longtext COMMENT '题目内容',
  `topic_answer` longtext COMMENT '参考答案',
  `collection` bit(1) DEFAULT b'0' COMMENT '是否收藏',
  PRIMARY KEY (`id`),
  KEY `idx_paper_id` (`paper_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目信息表';
```

**字段说明**:
- `paper_id`: 外键，关联试卷表
- `topic_content`: 题目内容，支持富文本和代码
- `topic_answer`: 参考答案，选择题存储选项标识，编程题存储代码

#### 3. a7_topic_option (题目选项表)
存储选择题的选项信息。

```sql
CREATE TABLE `a7_topic_option` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` varchar(255) DEFAULT NULL COMMENT '创建部门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `topic_id` bigint DEFAULT NULL COMMENT '所属题目ID',
  `sorted` varchar(20) DEFAULT NULL COMMENT '选项排序(A/B/C/D)',
  `option_content` varchar(255) DEFAULT NULL COMMENT '选项内容',
  `right_answer` bit(1) DEFAULT b'0' COMMENT '是否正确答案',
  PRIMARY KEY (`id`),
  KEY `idx_topic_id` (`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目选项表';
```

**字段说明**:
- `topic_id`: 外键，关联题目表
- `sorted`: 选项排序标识，如A、B、C、D
- `right_answer`: 标识是否为正确答案

#### 4. a7_paper_answer (答题记录表)
存储学生的答题记录。

```sql
CREATE TABLE `a7_paper_answer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` varchar(255) DEFAULT NULL COMMENT '创建部门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `paper_id` bigint DEFAULT NULL COMMENT '试卷ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `topic_id` bigint DEFAULT NULL COMMENT '题目ID',
  `topic_write` longtext COMMENT '学生答案',
  `right_answer` bit(1) DEFAULT b'0' COMMENT '是否正确',
  PRIMARY KEY (`id`),
  KEY `idx_paper_user` (`paper_id`, `user_id`),
  KEY `idx_topic_id` (`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='答题记录表';
```

**字段说明**:
- `topic_write`: 学生填写的答案
- `right_answer`: 答案是否正确，选择题自动判断，编程题需人工评分

#### 5. a7_paper_user_relation (试卷用户关系表)
记录用户与试卷的关系，包括答题状态和完成时间。

```sql
CREATE TABLE `a7_paper_user_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` varchar(255) DEFAULT NULL COMMENT '创建部门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `paper_id` bigint DEFAULT NULL COMMENT '试卷ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `finished` bit(1) DEFAULT b'0' COMMENT '是否完成',
  `finish_time` int DEFAULT NULL COMMENT '完成时间(分钟)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_paper_user` (`paper_id`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='试卷用户关系表';
```

**字段说明**:
- `finished`: 标识用户是否完成该试卷
- `finish_time`: 用户实际答题用时

### B4 课题相关表

#### 6. b4_video_course (视频课程表)
存储视频课程的基本信息。

```sql
CREATE TABLE `b4_video_course` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` varchar(255) DEFAULT NULL COMMENT '创建部门',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `video_name` varchar(120) DEFAULT NULL COMMENT '视频名称',
  `video_type` varchar(64) DEFAULT NULL COMMENT '视频类型',
  `video_size` bigint DEFAULT NULL COMMENT '视频大小(字节)',
  `formatted_size` varchar(32) DEFAULT NULL COMMENT '格式化大小',
  `video_duration` decimal(10,2) DEFAULT NULL COMMENT '视频时长(秒)',
  `formatted_duration` varchar(32) DEFAULT NULL COMMENT '格式化时长',
  `collection` bit(1) DEFAULT b'0' COMMENT '是否收藏',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频课程表';
```

**字段说明**:
- `video_size`: 视频文件大小，单位字节
- `formatted_size`: 人类可读的文件大小，如"96.23 MB"
- `video_duration`: 视频时长，单位秒
- `formatted_duration`: 格式化的时长，如"8:47"

### AI 聊天相关表

#### 7. chat_model (AI模型配置表)
存储AI模型的配置信息。

```sql
CREATE TABLE `chat_model` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `category` varchar(20) DEFAULT NULL COMMENT '模型分类',
  `model_name` varchar(50) DEFAULT NULL COMMENT '模型名称',
  `model_describe` varchar(255) DEFAULT NULL COMMENT '模型描述',
  `model_price` double DEFAULT NULL COMMENT '模型价格',
  `model_type` char(1) DEFAULT NULL COMMENT '计费类型(1:按次数 2:按Token)',
  `model_show` char(1) DEFAULT NULL COMMENT '是否显示(0:隐藏 1:显示)',
  `system_prompt` varchar(255) DEFAULT NULL COMMENT '系统提示词',
  `api_host` varchar(255) DEFAULT NULL COMMENT 'API地址',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `api_url` varchar(50) DEFAULT NULL COMMENT 'API后缀',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型配置表';
```

**字段说明**:
- `category`: 模型分类，如chat、vector、image等
- `model_type`: 计费类型，1为按次数计费，2为按Token计费
- `model_show`: 控制模型是否在前端显示

#### 8. chat_message (聊天消息表)
存储用户与AI的聊天记录。

```sql
CREATE TABLE `chat_message` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `session_id` bigint DEFAULT NULL COMMENT '会话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `content` longtext COMMENT '消息内容',
  `role` varchar(255) DEFAULT NULL COMMENT '角色(user/assistant)',
  `deduct_cost` double(20,2) DEFAULT 0.00 COMMENT '扣除费用',
  `total_tokens` int DEFAULT 0 COMMENT '消耗Token数',
  `model_name` varchar(255) DEFAULT NULL COMMENT '使用的模型名称',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';
```

**字段说明**:
- `session_id`: 会话标识，用于关联同一次对话
- `role`: 消息角色，user表示用户，assistant表示AI
- `deduct_cost`: 本次对话扣除的费用
- `total_tokens`: 本次对话消耗的Token数量

#### 9. chat_config (系统配置表)
存储系统的各种配置信息。

```sql
CREATE TABLE `chat_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category` varchar(255) NOT NULL COMMENT '配置分类',
  `config_name` varchar(255) NOT NULL COMMENT '配置名称',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_dict` varchar(255) DEFAULT NULL COMMENT '配置说明',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `version` int DEFAULT NULL COMMENT '版本号',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `update_ip` varchar(128) DEFAULT NULL COMMENT '更新IP',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_category_key` (`category`, `config_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

**配置分类说明**:
- `chat`: AI聊天相关配置（API密钥、地址等）
- `pay`: 支付相关配置
- `mail`: 邮件服务配置
- `sys`: 系统基础配置

#### 10. chat_pay_order (支付订单表)
存储用户的支付订单信息。

```sql
CREATE TABLE `chat_pay_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(100) NOT NULL COMMENT '订单编号',
  `order_name` varchar(100) NOT NULL COMMENT '订单名称',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `payment_status` char(1) DEFAULT NULL COMMENT '支付状态',
  `payment_method` char(10) DEFAULT NULL COMMENT '支付方式',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';
```

## 数据库关系图

### A7 课题模块关系
```
a7_paper (试卷)
    ├── a7_topic (题目) [1:N]
    │   └── a7_topic_option (选项) [1:N]
    ├── a7_paper_answer (答题记录) [1:N]
    └── a7_paper_user_relation (用户关系) [1:N]
```

### AI 聊天模块关系
```
chat_model (模型配置)
    └── chat_message (聊天消息) [1:N]

chat_pay_order (支付订单)
    └── sys_user (用户) [N:1]
```

## 索引设计

### 主要索引
1. **a7_topic**: `idx_paper_id` - 根据试卷ID查询题目
2. **a7_topic_option**: `idx_topic_id` - 根据题目ID查询选项
3. **a7_paper_answer**: `idx_paper_user`, `idx_topic_id` - 答题记录查询
4. **a7_paper_user_relation**: `uk_paper_user` - 唯一约束，防止重复关系
5. **chat_message**: `idx_session_id`, `idx_user_id` - 聊天记录查询

### 性能优化建议
1. 对于大表，考虑按时间分区
2. 定期清理过期的聊天记录
3. 对频繁查询的字段建立复合索引
4. 使用读写分离提高查询性能

## 数据字典

### 枚举值定义

#### 试卷难度 (paper_difficulty)
- `基础`: 基础难度
- `中等`: 中等难度  
- `挑战`: 挑战难度

#### 试卷类型 (paper_type)
- `全部题型`: 包含所有类型题目
- `编程题`: 仅包含编程题
- `选择题`: 仅包含选择题

#### 题目类型 (topic_type)
- `选择题`: 单选或多选题
- `编程题`: 代码编程题

#### 模型分类 (category)
- `chat`: 对话模型
- `vector`: 向量模型
- `image`: 图像模型

#### 计费类型 (model_type)
- `1`: 按次数计费
- `2`: 按Token计费

## 数据备份策略

### 备份方案
1. **全量备份**: 每日凌晨进行全量备份
2. **增量备份**: 每小时进行增量备份
3. **日志备份**: 实时备份binlog日志

### 恢复策略
1. 保留最近30天的全量备份
2. 保留最近7天的增量备份
3. 关键表数据实时同步到备库

## 数据安全

### 敏感数据处理
1. **API密钥**: 加密存储，不在日志中输出
2. **用户密码**: 使用BCrypt加密
3. **个人信息**: 遵循数据保护法规

### 访问控制
1. 数据库用户权限最小化
2. 应用层数据权限控制
3. 审计日志记录所有数据变更

这个数据库设计支持系统的所有核心功能，具有良好的扩展性和性能表现。

# RuoYi-AI 教学系统技术文档

## 项目概述

RuoYi-AI 是一个基于 RuoYi 框架开发的智能教学系统，集成了 AI 聊天助手功能。系统主要包含两个核心课题：
- **A7课题**：试卷管理系统，支持在线考试、题目管理、自动评分等功能
- **B4课题**：视频课程系统，提供视频课程的管理和播放功能
- **AI聊天助手**：集成多种AI模型，提供智能对话服务

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.4.4
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0.33
- **ORM框架**: MyBatis Plus 3.5.11
- **权限认证**: Sa-Token 1.34.0
- **缓存**: Redis + Redisson 3.20.1
- **文档**: SpringDoc OpenAPI 2.8.5
- **工具库**: Hutool 5.8.35

### 前端技术栈
#### Chat-AI 前端
- **框架**: Vue 3.2.47 + TypeScript
- **构建工具**: Vite 4.2.0
- **UI组件**: Naive UI 2.34.3
- **状态管理**: Pinia 2.0.33
- **路由**: Vue Router 4.1.6
- **国际化**: Vue I18n 9.2.2

#### Subject-A7 管理后台
- **框架**: Vue 3 + TypeScript
- **架构**: Monorepo (pnpm workspace)
- **构建工具**: Turbo + Vite
- **UI组件**: Ant Design Vue (主要)、Element Plus、Naive UI
- **基础框架**: Vben Admin Pro

## 项目结构

### 后端模块结构
```
backend/
├── ruoyi-admin/                 # 主启动模块
│   ├── src/main/java/org/ruoyi/
│   │   ├── RuoYiAIApplication.java    # 启动类
│   │   └── controller/                # 控制器层
├── ruoyi-common/                # 公共组件库
│   ├── ruoyi-common-core/       # 核心组件
│   ├── ruoyi-common-web/        # Web组件
│   ├── ruoyi-common-mybatis/    # MyBatis配置
│   ├── ruoyi-common-redis/      # Redis配置
│   ├── ruoyi-common-satoken/    # Sa-Token配置
│   ├── ruoyi-common-chat/       # AI聊天组件
│   └── ...                      # 其他公共组件
├── ruoyi-modules/               # 业务模块
│   ├── ruoyi-chat/             # AI聊天模块
│   ├── ruoyi-system/           # 系统管理模块
│   └── ruoyi-generator/        # 代码生成模块
├── ruoyi-modules-api/          # API接口模块
│   ├── ruoyi-chat-api/         # 聊天API
│   ├── ruoyi-system-api/       # 系统API
│   ├── ruoyi-teaching-a7/      # A7课题API
│   └── ruoyi-teaching-b4/      # B4课题API
└── ruoyi-extend/               # 扩展模块
    └── ruoyi-mcp-server/       # MCP服务器
```

### 前端项目结构
```
frontend/
├── chat-ai/                    # AI聊天前端
│   ├── src/
│   │   ├── components/         # 组件
│   │   ├── views/             # 页面
│   │   ├── store/             # 状态管理
│   │   ├── router/            # 路由配置
│   │   └── api/               # API接口
│   ├── package.json
│   └── vite.config.ts
└── subject_a7/                 # 管理后台前端
    ├── apps/                   # 应用目录
    │   └── web-antd/          # Ant Design版本
    ├── packages/              # 公共包
    ├── internal/              # 内部工具
    └── docs/                  # 文档
```

## 数据库设计

### A7课题相关表
- **a7_paper**: 试卷信息表
  - 字段：试卷名称、难度、类型、科目、题目数量、答题时间等
- **a7_topic**: 题目信息表
  - 字段：题目名称、难度、类型、内容、参考答案等
- **a7_topic_option**: 题目选项表（选择题）
  - 字段：选项内容、是否正确答案等
- **a7_paper_answer**: 答题记录表
  - 字段：用户答案、是否正确等
- **a7_paper_user_relation**: 试卷用户关系表
  - 字段：是否完成、完成时间等

### B4课题相关表
- **b4_video_course**: 视频课程表
  - 字段：视频名称、类型、大小、时长等

### AI聊天相关表
- **chat_model**: AI模型配置表
  - 字段：模型名称、API地址、密钥、价格等
- **chat_message**: 聊天消息表
  - 字段：会话ID、用户ID、消息内容、角色等
- **chat_config**: 系统配置表
  - 字段：配置类型、配置名称、配置值等

## 核心功能模块

### 1. A7试卷管理系统

#### 试卷管理功能
- **创建试卷**: 教师可创建试卷，设置基本信息（名称、难度、类型、答题时间等）
- **题目管理**: 支持选择题和编程题两种类型
- **权限控制**: 教师只能管理自己创建的试卷，学生可查看教师发布的试卷

#### 答题功能
- **在线答题**: 学生可在线答题，系统实时保存答题进度
- **自动评分**: 选择题自动评分，编程题需人工评分
- **答题记录**: 完整记录学生答题过程和结果

#### 核心接口
```java
// 试卷管理
@PostMapping("/a7/paper")           // 创建试卷
@GetMapping("/a7/paper/list")       // 获取试卷列表
@PostMapping("/a7/paper/edit")      // 编辑试卷

// 题目管理  
@PostMapping("/a7/topic")           // 创建题目
@GetMapping("/a7/topic/list")       // 获取题目列表

// 答题相关
@PostMapping("/a7/paper/saveAnswer/{id}")  // 保存答案
```

### 2. B4视频课程系统

#### 视频管理功能
- **视频上传**: 支持多种格式视频上传
- **视频信息**: 自动解析视频时长、大小等信息
- **收藏功能**: 用户可收藏喜欢的视频课程

#### 核心接口
```java
@PostMapping("/b4/videoCourse")     // 上传视频
@GetMapping("/b4/videoCourse/list") // 获取视频列表
```

### 3. AI聊天系统

#### 多模型支持
系统采用策略模式，支持多种AI服务商：
- **OpenAI**: GPT系列模型
- **DeepSeek**: 深度求索模型
- **智谱清言**: GLM系列模型  
- **通义千问**: Qwen系列模型
- **本地模型**: Ollama部署的本地模型

#### 聊天服务实现
```java
public interface IChatService {
    SseEmitter chat(ChatRequest chatRequest, SseEmitter emitter);
    String getCategory();
}
```

每个AI服务商都有对应的实现类：
- `OpenAIServiceImpl`: OpenAI模型实现
- `DeepSeekChatImpl`: DeepSeek模型实现
- `ZhipuAiChatServiceImpl`: 智谱清言实现
- `QianWenAiChatServiceImpl`: 通义千问实现

#### 流式响应
使用SSE（Server-Sent Events）技术实现流式响应：
```java
@PostMapping("/chat/send")
public SseEmitter sseChat(@RequestBody ChatRequest chatRequest) {
    return sseService.sseChat(chatRequest);
}
```

#### 模型配置管理
- **动态配置**: 模型参数存储在数据库中，支持动态修改
- **计费管理**: 支持按次数和按Token两种计费方式
- **权限控制**: 不同用户可使用不同的模型

## 特殊功能接口

### 1. 文件上传
```java
@PostMapping("/chat/upload")
public UploadFileResponse upload(@RequestPart("file") MultipartFile file)
```
支持图片、文档等多种文件类型上传，用于多模态AI对话。

### 2. 语音转文本
```java
@PostMapping("/chat/audio") 
public WhisperResponse audio(@RequestParam("file") MultipartFile file)
```
集成Whisper API，支持语音转文字功能。

### 3. 支付集成
```java
@PostMapping("/pay/notifyUrl")  // 支付回调
@PostMapping("/pay/returnUrl")  // 支付跳转
```
支持第三方支付平台集成，用于付费功能。

### 4. 邮件服务
支持邮件验证码发送，配置灵活：
- SMTP服务器配置
- 邮件模板自定义
- 验证码有效期控制

## 系统配置

### 应用配置 (application.yml)
```yaml
server:
  port: 6039                    # 服务端口

ruoyi:
  name: "ruoyi"                # 项目名称
  version: ${revision}         # 版本号
  demoEnabled: true            # 演示模式开关

sa-token:
  token-name: Authorization    # Token名称
  timeout: 604800             # Token有效期(7天)
  is-concurrent: true         # 允许并发登录
```

### 数据库配置
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************
```

### Redis配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
```

## 部署指南

### 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Node.js 18+
- pnpm 9.12.0+

### 后端部署
1. 导入数据库脚本 `course_ai.sql`
2. 修改配置文件中的数据库连接信息
3. 执行 `mvn clean package` 打包
4. 运行 `java -jar ruoyi-admin.jar`

### 前端部署
#### Chat-AI前端
```bash
cd frontend/chat-ai
pnpm install
pnpm run build
```

#### Subject-A7管理后台
```bash
cd frontend/subject_a7  
pnpm install
pnpm run build:antd
```

### Docker部署
项目提供了Docker配置文件，支持容器化部署：
```bash
docker build -t ruoyi-ai .
docker run -p 6039:6039 ruoyi-ai
```

## 开发指南

### 添加新的AI模型
1. 实现 `IChatService` 接口
2. 在 `ChatModeType` 枚举中添加新类型
3. 在数据库中配置模型信息
4. 注册为Spring Bean

### 扩展新的业务模块
1. 在 `ruoyi-modules-api` 中创建API模块
2. 在 `ruoyi-modules` 中创建业务实现
3. 在 `ruoyi-admin` 中添加控制器
4. 更新前端页面和API调用

### 代码生成
系统集成了代码生成器，可快速生成CRUD代码：
1. 配置数据表信息
2. 选择生成模板
3. 一键生成前后端代码

## 总结

RuoYi-AI教学系统是一个功能完整、架构清晰的现代化教学平台。通过模块化设计和微服务架构，系统具有良好的可扩展性和可维护性。AI聊天功能的集成为传统教学系统注入了智能化元素，提升了用户体验。

系统采用了多种先进技术和设计模式，如策略模式、依赖注入、AOP等，代码质量较高。同时提供了完善的权限控制、数据权限、多租户等企业级功能，适合在实际教学环境中部署使用。

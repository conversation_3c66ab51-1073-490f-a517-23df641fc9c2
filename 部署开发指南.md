# RuoYi-AI 系统部署开发指南

## 环境要求

### 基础环境
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **JDK**: OpenJDK 17 或 Oracle JDK 17+
- **Node.js**: 18.0+ (推荐 18.17.0)
- **包管理器**: pnpm 9.12.0+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **IDE**: IntelliJ IDEA 2023+ 或 VS Code

### 开发工具
- **Maven**: 3.8.0+
- **Git**: 2.30.0+
- **Docker**: 20.10+ (可选)
- **Postman**: API测试工具

## 项目获取与初始化

### 1. 克隆项目
```bash
git clone <项目地址>
cd SourceCode
```

### 2. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE course_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

# 导入数据库脚本
mysql -u root -p course_ai < course_ai.sql
```

### 3. Redis 启动
```bash
# Windows
redis-server.exe

# Linux/macOS
redis-server
```

## 后端开发环境搭建

### 1. 导入项目
1. 打开 IntelliJ IDEA
2. 选择 "Open" 导入 `backend` 目录
3. 等待 Maven 依赖下载完成

### 2. 配置文件修改
编辑 `backend/ruoyi-admin/src/main/resources/application-dev.yml`:

```yaml
# 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: your_password

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
```

### 3. 启动后端服务
```bash
cd backend
mvn clean install
mvn spring-boot:run -pl ruoyi-admin
```

或在 IDE 中直接运行 `RuoYiAIApplication.java`

### 4. 验证启动
访问 `http://localhost:6039` 查看是否启动成功。

## 前端开发环境搭建

### 1. Chat-AI 前端
```bash
cd frontend/chat-ai

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev
```

访问 `http://localhost:5172` 查看聊天界面。

### 2. Subject-A7 管理后台
```bash
cd frontend/subject_a7

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev:antd
```

访问 `http://localhost:5173` 查看管理后台。

### 3. 环境变量配置
创建 `.env.development` 文件：

**Chat-AI 前端**:
```env
VITE_APP_API_BASE_URL=http://localhost:6039
VITE_APP_TITLE=AI助手
```

**Subject-A7 前端**:
```env
VITE_GLOB_API_URL=http://localhost:6039
VITE_GLOB_APP_CLIENT_ID=ruoyi-ai
VITE_APP_TITLE=教学管理系统
```

## 开发规范

### 1. 代码规范

#### 后端规范
- 使用 Google Java Style Guide
- 类名使用 PascalCase
- 方法名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE
- 包名使用小写字母

#### 前端规范
- 使用 ESLint + Prettier
- 组件名使用 PascalCase
- 变量名使用 camelCase
- 文件名使用 kebab-case

### 2. Git 提交规范
```bash
# 格式
<type>(<scope>): <subject>

# 示例
feat(chat): 添加语音转文字功能
fix(paper): 修复试卷保存问题
docs(api): 更新API文档
```

**Type 类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 3. 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 新功能开发流程

### 1. 添加新的业务模块

#### 后端开发步骤
1. **创建数据表**
```sql
CREATE TABLE `new_module` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  -- 其他字段
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

2. **生成基础代码**
使用系统自带的代码生成器：
- 访问管理后台的"系统工具" -> "代码生成"
- 导入数据表
- 配置生成信息
- 生成并下载代码

3. **创建实体类**
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("new_module")
public class NewModule extends BaseEntity {
    @TableId
    private Long id;
    private String name;
    // 其他字段
}
```

4. **创建服务接口和实现**
```java
public interface INewModuleService extends IService<NewModule> {
    // 业务方法
}

@Service
public class NewModuleServiceImpl extends ServiceImpl<NewModuleMapper, NewModule> 
    implements INewModuleService {
    // 实现业务逻辑
}
```

5. **创建控制器**
```java
@RestController
@RequestMapping("/system/newModule")
public class NewModuleController extends BaseController {
    
    @Autowired
    private INewModuleService newModuleService;
    
    @GetMapping("/list")
    public TableDataInfo<NewModuleVo> list(NewModuleBo bo, PageQuery pageQuery) {
        return newModuleService.queryPageList(bo, pageQuery);
    }
}
```

#### 前端开发步骤
1. **创建API接口**
```typescript
// src/api/system/newModule.ts
export interface NewModule {
  id?: number;
  name: string;
}

export function getNewModuleList(params: any) {
  return requestClient.get<PageResult<NewModule>>('/system/newModule/list', { params });
}

export function addNewModule(data: NewModule) {
  return requestClient.postWithMsg<void>('/system/newModule', data);
}
```

2. **创建页面组件**
```vue
<!-- src/views/system/newModule/index.vue -->
<template>
  <div>
    <!-- 页面内容 -->
  </div>
</template>

<script setup lang="ts">
import { getNewModuleList } from '@/api/system/newModule';
// 组件逻辑
</script>
```

3. **配置路由**
```typescript
// src/router/routes/modules/system.ts
{
  path: '/newModule',
  name: 'NewModule',
  component: () => import('@/views/system/newModule/index.vue'),
  meta: {
    title: '新模块管理',
    icon: 'icon-module'
  }
}
```

### 2. 添加新的AI模型

#### 实现步骤
1. **创建服务实现类**
```java
@Service
public class NewAiChatServiceImpl implements IChatService {
    
    @Override
    public SseEmitter chat(ChatRequest chatRequest, SseEmitter emitter) {
        // 实现AI模型调用逻辑
        return emitter;
    }
    
    @Override
    public String getCategory() {
        return "new_ai_model";
    }
}
```

2. **添加模型枚举**
```java
// ChatModeType.java
NEW_AI("new_ai", "新AI模型")
```

3. **数据库配置**
```sql
INSERT INTO chat_model (category, model_name, model_describe, api_host, api_key) 
VALUES ('chat', 'new-ai-model', '新AI模型', 'https://api.example.com', 'your-api-key');
```

## 测试指南

### 1. 单元测试
```java
@SpringBootTest
class NewModuleServiceTest {
    
    @Autowired
    private INewModuleService newModuleService;
    
    @Test
    void testQueryList() {
        // 测试逻辑
    }
}
```

### 2. 接口测试
使用 Postman 或编写集成测试：
```java
@AutoConfigureTestDatabase
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class NewModuleControllerTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetList() {
        // 测试API接口
    }
}
```

### 3. 前端测试
```bash
# 运行单元测试
pnpm run test:unit

# 运行E2E测试
pnpm run test:e2e
```

## 生产环境部署

### 1. 后端部署

#### 传统部署
```bash
# 打包
mvn clean package -P prod

# 运行
java -jar -Dspring.profiles.active=prod ruoyi-admin.jar
```

#### Docker 部署
```dockerfile
FROM openjdk:17-jre-slim

COPY ruoyi-admin.jar app.jar

EXPOSE 6039

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```bash
# 构建镜像
docker build -t ruoyi-ai:latest .

# 运行容器
docker run -d -p 6039:6039 --name ruoyi-ai ruoyi-ai:latest
```

### 2. 前端部署

#### 构建生产版本
```bash
# Chat-AI
cd frontend/chat-ai
pnpm run build

# Subject-A7
cd frontend/subject_a7
pnpm run build:antd
```

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # Chat-AI 前端
    location /chat {
        root /var/www/html/chat-ai;
        try_files $uri $uri/ /index.html;
    }
    
    # 管理后台
    location /admin {
        root /var/www/html/subject-a7;
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api {
        proxy_pass http://localhost:6039;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_create_time ON a7_paper(create_time);
CREATE INDEX idx_user_session ON chat_message(user_id, session_id);

-- 配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL max_connections = 1000;
```

## 监控与维护

### 1. 应用监控
- 使用 Spring Boot Actuator 监控应用状态
- 集成 Prometheus + Grafana 监控系统性能
- 配置日志收集和分析

### 2. 数据库监控
- 监控数据库连接数
- 监控慢查询
- 定期备份数据

### 3. 系统维护
- 定期清理日志文件
- 监控磁盘空间使用
- 更新安全补丁

## 常见问题解决

### 1. 启动问题
**问题**: 后端启动失败
**解决**: 检查数据库连接、Redis连接、端口占用

**问题**: 前端编译失败
**解决**: 清除 node_modules，重新安装依赖

### 2. 功能问题
**问题**: AI聊天无响应
**解决**: 检查API密钥配置、网络连接

**问题**: 文件上传失败
**解决**: 检查文件大小限制、存储路径权限

### 3. 性能问题
**问题**: 接口响应慢
**解决**: 添加数据库索引、优化SQL查询、启用缓存

**问题**: 前端加载慢
**解决**: 启用代码分割、CDN加速、图片压缩

## 技术支持

### 1. 文档资源
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [Vue 3 官方文档](https://vuejs.org/)
- [MyBatis Plus 文档](https://baomidou.com/)

### 2. 社区支持
- GitHub Issues
- 技术交流群
- 官方论坛

### 3. 商业支持
- 技术咨询服务
- 定制开发服务
- 培训服务

通过本指南，开发者可以快速搭建开发环境，理解项目架构，并进行功能扩展和部署。

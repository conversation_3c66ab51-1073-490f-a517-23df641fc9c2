# RuoYi-AI 系统 API 接口文档

## 接口概述

本文档详细描述了 RuoYi-AI 教学系统的所有 API 接口，包括 A7 试卷管理、B4 视频课程、AI 聊天等核心功能模块的接口规范。

## 通用说明

### 基础信息
- **Base URL**: `http://localhost:6039`
- **Content-Type**: `application/json`
- **认证方式**: <PERSON><PERSON> (Sa-Token)
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 分页响应格式
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [],
  "total": 100,
  "timestamp": 1640995200000
}
```

### 错误码说明
- `200`: 操作成功
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## A7 试卷管理模块

### 1. 试卷管理接口

#### 1.1 获取试卷列表
**接口地址**: `GET /a7/paper/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "paperName": "试卷名称",
  "paperDifficulty": "基础",
  "paperType": "选择题",
  "createRole": "teacher"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "paperName": "Java基础测试",
      "createRole": "teacher",
      "createName": "张老师",
      "paperDifficulty": "基础",
      "paperType": "选择题",
      "paperSubject": "计算机科学与技术",
      "topicNumber": 10,
      "answerTime": 60,
      "collection": false,
      "createTime": "2025-01-01 10:00:00"
    }
  ],
  "total": 1
}
```

#### 1.2 创建试卷
**接口地址**: `POST /a7/paper`

**请求参数**:
```json
{
  "paperName": "Java基础测试",
  "paperDifficulty": "基础",
  "paperType": "选择题",
  "paperSubject": "计算机科学与技术",
  "answerTime": 60,
  "topics": [
    {
      "topicName": "Java是什么？",
      "topicDifficulty": "基础",
      "topicType": "选择题",
      "topicContent": "Java是一种什么类型的编程语言？",
      "topicAnswer": "A",
      "topicOptions": [
        {
          "sorted": "A",
          "optionContent": "面向对象编程语言",
          "rightAnswer": true
        },
        {
          "sorted": "B", 
          "optionContent": "函数式编程语言",
          "rightAnswer": false
        }
      ]
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": null
}
```

#### 1.3 获取试卷详情
**接口地址**: `GET /a7/paper/{id}`

**路径参数**:
- `id`: 试卷ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "paperName": "Java基础测试",
    "createRole": "teacher",
    "createName": "张老师",
    "paperDifficulty": "基础",
    "paperType": "选择题",
    "paperSubject": "计算机科学与技术",
    "topicNumber": 10,
    "answerTime": 60,
    "collection": false,
    "topics": [
      {
        "id": 1,
        "topicName": "Java是什么？",
        "topicDifficulty": "基础",
        "topicType": "选择题",
        "topicContent": "Java是一种什么类型的编程语言？",
        "topicAnswer": "A",
        "topicOptions": [
          {
            "id": 1,
            "sorted": "A",
            "optionContent": "面向对象编程语言",
            "rightAnswer": true
          }
        ]
      }
    ]
  }
}
```

#### 1.4 保存答题结果
**接口地址**: `POST /a7/paper/saveAnswer/{id}`

**路径参数**:
- `id`: 试卷ID

**请求参数**:
```json
{
  "answers": [
    {
      "topicId": 1,
      "topicWrite": "A",
      "rightAnswer": true
    }
  ],
  "finishTime": 45
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "保存成功",
  "data": null
}
```

### 2. 题目管理接口

#### 2.1 获取题目列表
**接口地址**: `GET /a7/topic/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "paperId": 1,
  "topicName": "题目名称",
  "topicDifficulty": "基础",
  "topicType": "选择题"
}
```

#### 2.2 创建题目
**接口地址**: `POST /a7/topic`

**请求参数**:
```json
{
  "paperId": 1,
  "topicName": "Java是什么？",
  "topicDifficulty": "基础",
  "topicType": "选择题",
  "topicContent": "Java是一种什么类型的编程语言？",
  "topicAnswer": "A",
  "topicOptions": [
    {
      "sorted": "A",
      "optionContent": "面向对象编程语言",
      "rightAnswer": true
    }
  ]
}
```

## B4 视频课程模块

### 1. 视频课程管理接口

#### 1.1 获取视频课程列表
**接口地址**: `GET /b4/videoCourse/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "videoName": "视频名称",
  "videoType": "mp4"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "videoName": "生物神经元与神经网络",
      "videoType": "mp4",
      "videoSize": 100908985,
      "formattedSize": "96.23 MB",
      "videoDuration": 527.30,
      "formattedDuration": "8:47",
      "collection": false,
      "createTime": "2025-07-06 20:24:10"
    }
  ],
  "total": 1
}
```

#### 1.2 上传视频课程
**接口地址**: `POST /b4/videoCourse`

**请求参数**:
```json
{
  "videoName": "生物神经元与神经网络",
  "videoType": "mp4",
  "videoSize": 100908985,
  "formattedSize": "96.23 MB",
  "videoDuration": 527.30,
  "formattedDuration": "8:47"
}
```

## AI 聊天模块

### 1. 聊天接口

#### 1.1 发送聊天消息
**接口地址**: `POST /chat/send`

**请求参数**:
```json
{
  "model": "gpt-4o-mini",
  "prompt": "你好，请介绍一下Java编程语言",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下Java编程语言"
    }
  ],
  "userId": 1,
  "sessionId": 123456,
  "stream": true
}
```

**响应格式**: 
- 使用 Server-Sent Events (SSE) 流式返回
- Content-Type: `text/event-stream`

**响应示例**:
```
data: Java是一种
data: 面向对象的
data: 编程语言
data: [DONE]
```

#### 1.2 文件上传
**接口地址**: `POST /chat/upload`

**请求参数**: 
- Content-Type: `multipart/form-data`
- 参数: `file` (文件)

**响应示例**:
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "url": "http://example.com/uploads/image.jpg",
    "fileName": "image.jpg",
    "fileSize": 1024000
  }
}
```

#### 1.3 语音转文本
**接口地址**: `POST /chat/audio`

**请求参数**:
- Content-Type: `multipart/form-data`
- 参数: `file` (音频文件)

**响应示例**:
```json
{
  "code": 200,
  "msg": "转换成功",
  "data": {
    "text": "你好，请介绍一下Java编程语言",
    "duration": 3.5
  }
}
```

### 2. 模型管理接口

#### 2.1 获取模型列表
**接口地址**: `GET /system/model/modelList`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "category": "chat",
      "modelName": "gpt-4o-mini",
      "modelDescribe": "gpt 多模态模型",
      "modelPrice": 0.1,
      "modelType": "1",
      "modelShow": "1",
      "apiHost": "https://api.openai.com",
      "apiKey": "sk-xxx"
    }
  ]
}
```

## 系统管理模块

### 1. 用户管理接口

#### 1.1 用户登录
**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "admin123",
  "code": "1234",
  "uuid": "uuid-string"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "access_token": "Bearer eyJhbGciOiJIUzI1NiJ9...",
    "expires_in": 604800,
    "user_info": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>"
    }
  }
}
```

#### 1.2 获取用户信息
**接口地址**: `GET /system/user/getInfo`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "user": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "13888888888",
      "sex": "1",
      "avatar": "",
      "status": "0"
    },
    "roles": ["admin"],
    "permissions": ["*:*:*"]
  }
}
```

### 2. 配置管理接口

#### 2.1 获取系统配置
**接口地址**: `GET /system/config/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "configName": "配置名称",
  "configKey": "配置键名"
}
```

## 错误处理

### 常见错误响应

#### 401 未授权
```json
{
  "code": 401,
  "msg": "未授权，请先登录",
  "data": null
}
```

#### 403 权限不足
```json
{
  "code": 403,
  "msg": "权限不足，无法访问该资源",
  "data": null
}
```

#### 500 服务器错误
```json
{
  "code": 500,
  "msg": "服务器内部错误",
  "data": null
}
```

## 接口调用示例

### JavaScript 调用示例
```javascript
// 获取试卷列表
const getPaperList = async () => {
  const response = await fetch('/a7/paper/list?pageNum=1&pageSize=10', {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

// 发送聊天消息 (SSE)
const sendChatMessage = (message) => {
  const eventSource = new EventSource('/chat/send', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      prompt: message,
      userId: 1,
      sessionId: Date.now()
    })
  });
  
  eventSource.onmessage = (event) => {
    console.log('收到消息:', event.data);
  };
  
  eventSource.onerror = (error) => {
    console.error('连接错误:', error);
    eventSource.close();
  };
};
```

## 注意事项

1. **认证**: 除了登录接口和部分公开接口外，其他接口都需要在请求头中携带有效的 Token
2. **权限**: 不同角色的用户对接口的访问权限不同，请确保用户有相应的权限
3. **限流**: 部分接口可能有访问频率限制，请合理控制请求频率
4. **文件上传**: 文件上传接口有大小限制，请检查文件大小是否符合要求
5. **SSE连接**: 聊天接口使用SSE技术，需要保持连接状态来接收流式响应

## 更新日志

- **v1.0.0** (2025-01-01): 初始版本，包含基础功能接口
- **v1.1.0** (2025-01-15): 新增AI聊天功能接口
- **v1.2.0** (2025-02-01): 优化试卷管理接口，新增批量操作
